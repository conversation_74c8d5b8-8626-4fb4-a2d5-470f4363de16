{"ast": null, "code": "import axios from 'axios';\nimport toast from 'react-hot-toast';\n\n// 创建 axios 实例\nexport const authAPI = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8080',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 请求拦截器\nauthAPI.interceptors.request.use(config => {\n  // 添加认证 token\n  const token = localStorage.getItem('authToken');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n\n  // 添加请求 ID\n  config.headers['X-Request-ID'] = generateRequestId();\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\nauthAPI.interceptors.response.use(response => {\n  return response;\n}, error => {\n  var _data$error;\n  // 处理常见错误\n  if (error.response) {\n    const {\n      status,\n      data\n    } = error.response;\n    switch (status) {\n      case 401:\n        // 未授权，清除本地认证信息\n        localStorage.removeItem('authToken');\n        localStorage.removeItem('userData');\n        toast.error('登录已过期，请重新登录');\n        // 可以在这里重定向到登录页面\n        window.location.href = '/login';\n        break;\n      case 403:\n        toast.error('没有权限访问此资源');\n        break;\n      case 404:\n        toast.error('请求的资源不存在');\n        break;\n      case 429:\n        toast.error('请求过于频繁，请稍后再试');\n        break;\n      case 500:\n        toast.error('服务器内部错误');\n        break;\n      default:\n        // 显示后端返回的错误信息\n        const message = (data === null || data === void 0 ? void 0 : data.message) || (data === null || data === void 0 ? void 0 : (_data$error = data.error) === null || _data$error === void 0 ? void 0 : _data$error.message) || '请求失败';\n        toast.error(message);\n    }\n  } else if (error.request) {\n    // 网络错误\n    toast.error('网络连接失败，请检查网络设置');\n  } else {\n    // 其他错误\n    toast.error('请求配置错误');\n  }\n  return Promise.reject(error);\n});\n\n// 用户相关 API\nexport const userAPI = {\n  // 获取用户列表\n  getUsers: (params = {}) => {\n    return authAPI.get('/api/v1/customers', {\n      params\n    });\n  },\n  // 获取单个用户\n  getUser: id => {\n    return authAPI.get(`/api/v1/customers/${id}`);\n  },\n  // 创建用户（注册）\n  createUser: userData => {\n    return authAPI.post('/api/v1/customers', userData);\n  },\n  // 更新用户\n  updateUser: (id, userData) => {\n    return authAPI.put(`/api/v1/customers/${id}`, userData);\n  },\n  // 删除用户\n  deleteUser: id => {\n    return authAPI.delete(`/api/v1/customers/${id}`);\n  },\n  // 批量删除用户\n  batchDeleteUsers: ids => {\n    return authAPI.delete('/api/v1/customers/batch', {\n      data: {\n        ids\n      }\n    });\n  },\n  // 更新用户状态\n  updateUserStatus: (id, status) => {\n    return authAPI.patch(`/api/v1/customers/${id}/status`, {\n      status\n    });\n  },\n  // 搜索用户\n  searchUsers: keyword => {\n    return authAPI.get('/api/v1/customers/search', {\n      params: {\n        keyword\n      }\n    });\n  },\n  // 获取用户统计\n  getUserStats: () => {\n    return authAPI.get('/api/v1/customers/stats');\n  }\n};\n\n// 系统相关 API\nexport const systemAPI = {\n  // 健康检查\n  healthCheck: () => {\n    return authAPI.get('/health');\n  },\n  // 获取系统指标\n  getMetrics: () => {\n    return authAPI.get('/metrics');\n  }\n};\n\n// 认证相关 API\nexport const authService = {\n  // 登录\n  login: async credentials => {\n    return authAPI.post('/api/v1/auth/login', credentials);\n  },\n  // 注册\n  register: async userData => {\n    return authAPI.post('/api/v1/auth/register', userData);\n  },\n  // 验证 token\n  verifyToken: async () => {\n    return authAPI.post('/api/v1/auth/validate');\n  },\n  // 获取用户信息\n  getProfile: async () => {\n    return authAPI.get('/api/v1/profile');\n  },\n  // 修改密码\n  changePassword: async passwordData => {\n    return authAPI.post('/api/v1/change-password', passwordData);\n  }\n};\n\n// 工具函数\nfunction generateRequestId() {\n  return 'req_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();\n}\nexport default authAPI;", "map": {"version": 3, "names": ["axios", "toast", "authAPI", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "generateRequestId", "error", "Promise", "reject", "response", "_data$error", "status", "data", "removeItem", "window", "location", "href", "message", "userAPI", "getUsers", "params", "get", "getUser", "id", "createUser", "userData", "post", "updateUser", "put", "deleteUser", "delete", "batchDeleteUsers", "ids", "updateUserStatus", "patch", "searchUsers", "keyword", "getUserStats", "systemAPI", "healthCheck", "getMetrics", "authService", "login", "credentials", "register", "verifyToken", "getProfile", "changePassword", "passwordData", "Math", "random", "toString", "substr", "Date", "now"], "sources": ["D:/gohomeworkproject/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\nimport toast from 'react-hot-toast';\n\n// 创建 axios 实例\nexport const authAPI = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8080',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 请求拦截器\nauthAPI.interceptors.request.use(\n  (config) => {\n    // 添加认证 token\n    const token = localStorage.getItem('authToken');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    \n    // 添加请求 ID\n    config.headers['X-Request-ID'] = generateRequestId();\n    \n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\nauthAPI.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    // 处理常见错误\n    if (error.response) {\n      const { status, data } = error.response;\n      \n      switch (status) {\n        case 401:\n          // 未授权，清除本地认证信息\n          localStorage.removeItem('authToken');\n          localStorage.removeItem('userData');\n          toast.error('登录已过期，请重新登录');\n          // 可以在这里重定向到登录页面\n          window.location.href = '/login';\n          break;\n          \n        case 403:\n          toast.error('没有权限访问此资源');\n          break;\n          \n        case 404:\n          toast.error('请求的资源不存在');\n          break;\n          \n        case 429:\n          toast.error('请求过于频繁，请稍后再试');\n          break;\n          \n        case 500:\n          toast.error('服务器内部错误');\n          break;\n          \n        default:\n          // 显示后端返回的错误信息\n          const message = data?.message || data?.error?.message || '请求失败';\n          toast.error(message);\n      }\n    } else if (error.request) {\n      // 网络错误\n      toast.error('网络连接失败，请检查网络设置');\n    } else {\n      // 其他错误\n      toast.error('请求配置错误');\n    }\n    \n    return Promise.reject(error);\n  }\n);\n\n// 用户相关 API\nexport const userAPI = {\n  // 获取用户列表\n  getUsers: (params = {}) => {\n    return authAPI.get('/api/v1/customers', { params });\n  },\n  \n  // 获取单个用户\n  getUser: (id) => {\n    return authAPI.get(`/api/v1/customers/${id}`);\n  },\n  \n  // 创建用户（注册）\n  createUser: (userData) => {\n    return authAPI.post('/api/v1/customers', userData);\n  },\n  \n  // 更新用户\n  updateUser: (id, userData) => {\n    return authAPI.put(`/api/v1/customers/${id}`, userData);\n  },\n  \n  // 删除用户\n  deleteUser: (id) => {\n    return authAPI.delete(`/api/v1/customers/${id}`);\n  },\n  \n  // 批量删除用户\n  batchDeleteUsers: (ids) => {\n    return authAPI.delete('/api/v1/customers/batch', { data: { ids } });\n  },\n  \n  // 更新用户状态\n  updateUserStatus: (id, status) => {\n    return authAPI.patch(`/api/v1/customers/${id}/status`, { status });\n  },\n  \n  // 搜索用户\n  searchUsers: (keyword) => {\n    return authAPI.get('/api/v1/customers/search', { \n      params: { keyword } \n    });\n  },\n  \n  // 获取用户统计\n  getUserStats: () => {\n    return authAPI.get('/api/v1/customers/stats');\n  }\n};\n\n// 系统相关 API\nexport const systemAPI = {\n  // 健康检查\n  healthCheck: () => {\n    return authAPI.get('/health');\n  },\n  \n  // 获取系统指标\n  getMetrics: () => {\n    return authAPI.get('/metrics');\n  }\n};\n\n// 认证相关 API\nexport const authService = {\n  // 登录\n  login: async (credentials) => {\n    return authAPI.post('/api/v1/auth/login', credentials);\n  },\n\n  // 注册\n  register: async (userData) => {\n    return authAPI.post('/api/v1/auth/register', userData);\n  },\n\n  // 验证 token\n  verifyToken: async () => {\n    return authAPI.post('/api/v1/auth/validate');\n  },\n\n  // 获取用户信息\n  getProfile: async () => {\n    return authAPI.get('/api/v1/profile');\n  },\n\n  // 修改密码\n  changePassword: async (passwordData) => {\n    return authAPI.post('/api/v1/change-password', passwordData);\n  }\n};\n\n// 工具函数\nfunction generateRequestId() {\n  return 'req_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();\n}\n\n\n\nexport default authAPI;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,iBAAiB;;AAEnC;AACA,OAAO,MAAMC,OAAO,GAAGF,KAAK,CAACG,MAAM,CAAC;EAClCC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;EACjEC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAP,OAAO,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CAC7BC,MAAM,IAAK;EACV;EACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAC/C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;;EAEA;EACAD,MAAM,CAACJ,OAAO,CAAC,cAAc,CAAC,GAAGS,iBAAiB,CAAC,CAAC;EAEpD,OAAOL,MAAM;AACf,CAAC,EACAM,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAjB,OAAO,CAACQ,YAAY,CAACY,QAAQ,CAACV,GAAG,CAC9BU,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAI,WAAA;EACT;EACA,IAAIJ,KAAK,CAACG,QAAQ,EAAE;IAClB,MAAM;MAAEE,MAAM;MAAEC;IAAK,CAAC,GAAGN,KAAK,CAACG,QAAQ;IAEvC,QAAQE,MAAM;MACZ,KAAK,GAAG;QACN;QACAT,YAAY,CAACW,UAAU,CAAC,WAAW,CAAC;QACpCX,YAAY,CAACW,UAAU,CAAC,UAAU,CAAC;QACnCzB,KAAK,CAACkB,KAAK,CAAC,aAAa,CAAC;QAC1B;QACAQ,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;QAC/B;MAEF,KAAK,GAAG;QACN5B,KAAK,CAACkB,KAAK,CAAC,WAAW,CAAC;QACxB;MAEF,KAAK,GAAG;QACNlB,KAAK,CAACkB,KAAK,CAAC,UAAU,CAAC;QACvB;MAEF,KAAK,GAAG;QACNlB,KAAK,CAACkB,KAAK,CAAC,cAAc,CAAC;QAC3B;MAEF,KAAK,GAAG;QACNlB,KAAK,CAACkB,KAAK,CAAC,SAAS,CAAC;QACtB;MAEF;QACE;QACA,MAAMW,OAAO,GAAG,CAAAL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,OAAO,MAAIL,IAAI,aAAJA,IAAI,wBAAAF,WAAA,GAAJE,IAAI,CAAEN,KAAK,cAAAI,WAAA,uBAAXA,WAAA,CAAaO,OAAO,KAAI,MAAM;QAC/D7B,KAAK,CAACkB,KAAK,CAACW,OAAO,CAAC;IACxB;EACF,CAAC,MAAM,IAAIX,KAAK,CAACR,OAAO,EAAE;IACxB;IACAV,KAAK,CAACkB,KAAK,CAAC,gBAAgB,CAAC;EAC/B,CAAC,MAAM;IACL;IACAlB,KAAK,CAACkB,KAAK,CAAC,QAAQ,CAAC;EACvB;EAEA,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMY,OAAO,GAAG;EACrB;EACAC,QAAQ,EAAEA,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK;IACzB,OAAO/B,OAAO,CAACgC,GAAG,CAAC,mBAAmB,EAAE;MAAED;IAAO,CAAC,CAAC;EACrD,CAAC;EAED;EACAE,OAAO,EAAGC,EAAE,IAAK;IACf,OAAOlC,OAAO,CAACgC,GAAG,CAAC,qBAAqBE,EAAE,EAAE,CAAC;EAC/C,CAAC;EAED;EACAC,UAAU,EAAGC,QAAQ,IAAK;IACxB,OAAOpC,OAAO,CAACqC,IAAI,CAAC,mBAAmB,EAAED,QAAQ,CAAC;EACpD,CAAC;EAED;EACAE,UAAU,EAAEA,CAACJ,EAAE,EAAEE,QAAQ,KAAK;IAC5B,OAAOpC,OAAO,CAACuC,GAAG,CAAC,qBAAqBL,EAAE,EAAE,EAAEE,QAAQ,CAAC;EACzD,CAAC;EAED;EACAI,UAAU,EAAGN,EAAE,IAAK;IAClB,OAAOlC,OAAO,CAACyC,MAAM,CAAC,qBAAqBP,EAAE,EAAE,CAAC;EAClD,CAAC;EAED;EACAQ,gBAAgB,EAAGC,GAAG,IAAK;IACzB,OAAO3C,OAAO,CAACyC,MAAM,CAAC,yBAAyB,EAAE;MAAElB,IAAI,EAAE;QAAEoB;MAAI;IAAE,CAAC,CAAC;EACrE,CAAC;EAED;EACAC,gBAAgB,EAAEA,CAACV,EAAE,EAAEZ,MAAM,KAAK;IAChC,OAAOtB,OAAO,CAAC6C,KAAK,CAAC,qBAAqBX,EAAE,SAAS,EAAE;MAAEZ;IAAO,CAAC,CAAC;EACpE,CAAC;EAED;EACAwB,WAAW,EAAGC,OAAO,IAAK;IACxB,OAAO/C,OAAO,CAACgC,GAAG,CAAC,0BAA0B,EAAE;MAC7CD,MAAM,EAAE;QAAEgB;MAAQ;IACpB,CAAC,CAAC;EACJ,CAAC;EAED;EACAC,YAAY,EAAEA,CAAA,KAAM;IAClB,OAAOhD,OAAO,CAACgC,GAAG,CAAC,yBAAyB,CAAC;EAC/C;AACF,CAAC;;AAED;AACA,OAAO,MAAMiB,SAAS,GAAG;EACvB;EACAC,WAAW,EAAEA,CAAA,KAAM;IACjB,OAAOlD,OAAO,CAACgC,GAAG,CAAC,SAAS,CAAC;EAC/B,CAAC;EAED;EACAmB,UAAU,EAAEA,CAAA,KAAM;IAChB,OAAOnD,OAAO,CAACgC,GAAG,CAAC,UAAU,CAAC;EAChC;AACF,CAAC;;AAED;AACA,OAAO,MAAMoB,WAAW,GAAG;EACzB;EACAC,KAAK,EAAE,MAAOC,WAAW,IAAK;IAC5B,OAAOtD,OAAO,CAACqC,IAAI,CAAC,oBAAoB,EAAEiB,WAAW,CAAC;EACxD,CAAC;EAED;EACAC,QAAQ,EAAE,MAAOnB,QAAQ,IAAK;IAC5B,OAAOpC,OAAO,CAACqC,IAAI,CAAC,uBAAuB,EAAED,QAAQ,CAAC;EACxD,CAAC;EAED;EACAoB,WAAW,EAAE,MAAAA,CAAA,KAAY;IACvB,OAAOxD,OAAO,CAACqC,IAAI,CAAC,uBAAuB,CAAC;EAC9C,CAAC;EAED;EACAoB,UAAU,EAAE,MAAAA,CAAA,KAAY;IACtB,OAAOzD,OAAO,CAACgC,GAAG,CAAC,iBAAiB,CAAC;EACvC,CAAC;EAED;EACA0B,cAAc,EAAE,MAAOC,YAAY,IAAK;IACtC,OAAO3D,OAAO,CAACqC,IAAI,CAAC,yBAAyB,EAAEsB,YAAY,CAAC;EAC9D;AACF,CAAC;;AAED;AACA,SAAS3C,iBAAiBA,CAAA,EAAG;EAC3B,OAAO,MAAM,GAAG4C,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;AAC5E;AAIA,eAAejE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}