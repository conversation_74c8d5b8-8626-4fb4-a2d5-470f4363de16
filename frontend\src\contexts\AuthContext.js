import React, { createContext, useContext, useState, useEffect } from 'react';
import { authAPI } from '../services/api';
import toast from 'react-hot-toast';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // 检查本地存储的认证状态
  useEffect(() => {
    const checkAuthStatus = () => {
      try {
        const token = localStorage.getItem('authToken');
        const userData = localStorage.getItem('userData');
        
        if (token && userData) {
          const parsedUser = JSON.parse(userData);
          setUser(parsedUser);
          setIsAuthenticated(true);
          // 设置 API 默认 header
          authAPI.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        }
      } catch (error) {
        console.error('检查认证状态失败:', error);
        // 清除无效的本地存储
        localStorage.removeItem('authToken');
        localStorage.removeItem('userData');
      } finally {
        setLoading(false);
      }
    };

    checkAuthStatus();
  }, []);

  // 登录函数
  const login = async (credentials) => {
    try {
      setLoading(true);

      // 调用真实的登录 API
      const response = await authAPI.post('/api/v1/auth/login', credentials);

      if (response.data.success) {
        const { user: userData, token } = response.data.data;

        // 保存到本地存储
        localStorage.setItem('authToken', token);
        localStorage.setItem('userData', JSON.stringify(userData));

        // 设置 API 默认 header
        authAPI.defaults.headers.common['Authorization'] = `Bearer ${token}`;

        // 更新状态
        setUser(userData);
        setIsAuthenticated(true);

        toast.success('登录成功！');
        return { success: true };
      } else {
        toast.error(response.data.message || '登录失败');
        return { success: false, message: response.data.message };
      }
    } catch (error) {
      console.error('登录错误:', error);
      const message = error.response?.data?.message || '登录失败，请稍后重试';
      toast.error(message);
      return { success: false, message };
    } finally {
      setLoading(false);
    }
  };

  // 注册函数
  const register = async (userData) => {
    try {
      setLoading(true);

      // 调用真实的注册 API
      const response = await authAPI.post('/api/v1/auth/register', userData);

      if (response.data.success) {
        toast.success('注册成功！请登录');
        return { success: true };
      } else {
        toast.error(response.data.message || '注册失败');
        return { success: false, message: response.data.message };
      }
    } catch (error) {
      console.error('注册错误:', error);
      const message = error.response?.data?.message || '注册失败，请稍后重试';
      toast.error(message);
      return { success: false, message };
    } finally {
      setLoading(false);
    }
  };

  // 登出函数
  const logout = () => {
    try {
      // 清除本地存储
      localStorage.removeItem('authToken');
      localStorage.removeItem('userData');
      
      // 清除 API header
      delete authAPI.defaults.headers.common['Authorization'];
      
      // 重置状态
      setUser(null);
      setIsAuthenticated(false);
      
      toast.success('已安全退出');
    } catch (error) {
      console.error('登出错误:', error);
    }
  };

  // 更新用户信息
  const updateUser = (newUserData) => {
    try {
      const updatedUser = { ...user, ...newUserData };
      setUser(updatedUser);
      localStorage.setItem('userData', JSON.stringify(updatedUser));
    } catch (error) {
      console.error('更新用户信息失败:', error);
    }
  };

  const value = {
    user,
    loading,
    isAuthenticated,
    login,
    register,
    logout,
    updateUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};


