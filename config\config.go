package config

import (
	"log"
	"os"
	"strconv"
	"strings"
	"time"
)

// Config 应用配置
type Config struct {
	Server    ServerConfig    `json:"server"`
	Database  DatabaseConfig  `json:"database"`
	Logger    LoggerConfig    `json:"logger"`
	CORS      CORSConfig      `json:"cors"`
	RateLimit RateLimitConfig `json:"rate_limit"`
	JWT       JWTConfig       `json:"jwt"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host         string        `json:"host"`
	Port         string        `json:"port"`
	Mode         string        `json:"mode"` // debug, release, test
	ReadTimeout  time.Duration `json:"read_timeout"`
	WriteTimeout time.Duration `json:"write_timeout"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Type     string `json:"type"` // memory, mysql, postgres
	Host     string `json:"host"`
	Port     string `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	Database string `json:"database"`
	SSLMode  string `json:"ssl_mode"`
}

// LoggerConfig 日志配置
type LoggerConfig struct {
	Level      string   `json:"level"`  // debug, info, warn, error
	Format     string   `json:"format"` // json, text
	Output     string   `json:"output"` // stdout, file
	Filename   string   `json:"filename"`
	MaxSize    int      `json:"max_size"` // MB
	MaxBackups int      `json:"max_backups"`
	MaxAge     int      `json:"max_age"` // days
	SkipPaths  []string `json:"skip_paths"`
}

// CORSConfig CORS配置
type CORSConfig struct {
	AllowOrigins     []string `json:"allow_origins"`
	AllowMethods     []string `json:"allow_methods"`
	AllowHeaders     []string `json:"allow_headers"`
	ExposeHeaders    []string `json:"expose_headers"`
	AllowCredentials bool     `json:"allow_credentials"`
	MaxAge           int      `json:"max_age"` // seconds
}

// RateLimitConfig 速率限制配置
type RateLimitConfig struct {
	Enabled bool          `json:"enabled"`
	Rate    int           `json:"rate"`   // requests per window
	Window  time.Duration `json:"window"` // time window
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret string `json:"secret"`
}

// Load 加载配置
func Load() *Config {
	config := &Config{
		Server: ServerConfig{
			Host:         getEnv("SERVER_HOST", "0.0.0.0"),
			Port:         getEnv("SERVER_PORT", "8080"),
			Mode:         getEnv("GIN_MODE", "debug"),
			ReadTimeout:  getDurationEnv("SERVER_READ_TIMEOUT", 30*time.Second),
			WriteTimeout: getDurationEnv("SERVER_WRITE_TIMEOUT", 30*time.Second),
		},
		Database: DatabaseConfig{
			Type:     getEnv("DB_TYPE", "memory"),
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnv("DB_PORT", "3306"),
			Username: getEnv("DB_USERNAME", ""),
			Password: getEnv("DB_PASSWORD", ""),
			Database: getEnv("DB_DATABASE", ""),
			SSLMode:  getEnv("DB_SSL_MODE", "disable"),
		},
		Logger: LoggerConfig{
			Level:      getEnv("LOG_LEVEL", "info"),
			Format:     getEnv("LOG_FORMAT", "json"),
			Output:     getEnv("LOG_OUTPUT", "stdout"),
			Filename:   getEnv("LOG_FILENAME", "app.log"),
			MaxSize:    getIntEnv("LOG_MAX_SIZE", 100),
			MaxBackups: getIntEnv("LOG_MAX_BACKUPS", 3),
			MaxAge:     getIntEnv("LOG_MAX_AGE", 28),
			SkipPaths:  getSliceEnv("LOG_SKIP_PATHS", []string{"/health", "/metrics"}),
		},
		CORS: CORSConfig{
			AllowOrigins: getSliceEnv("CORS_ALLOW_ORIGINS", []string{
				"http://localhost:3000",
				"http://localhost:5173",
				"http://localhost:8080",
			}),
			AllowMethods: getSliceEnv("CORS_ALLOW_METHODS", []string{
				"GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH",
			}),
			AllowHeaders: getSliceEnv("CORS_ALLOW_HEADERS", []string{
				"Origin", "Content-Type", "Accept", "Authorization", "X-Requested-With", "X-Request-ID",
			}),
			ExposeHeaders:    getSliceEnv("CORS_EXPOSE_HEADERS", []string{}),
			AllowCredentials: getBoolEnv("CORS_ALLOW_CREDENTIALS", true),
			MaxAge:           getIntEnv("CORS_MAX_AGE", 86400),
		},
		RateLimit: RateLimitConfig{
			Enabled: getBoolEnv("RATE_LIMIT_ENABLED", true),
			Rate:    getIntEnv("RATE_LIMIT_RATE", 100),
			Window:  getDurationEnv("RATE_LIMIT_WINDOW", time.Minute),
		},
		JWT: JWTConfig{
			Secret: getEnv("JWT_SECRET", "your-secret-key-change-in-production"),
		},
	}

	log.Printf("配置加载完成: Server=%s:%s, Mode=%s", config.Server.Host, config.Server.Port, config.Server.Mode)
	return config
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getIntEnv 获取整数环境变量
func getIntEnv(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getBoolEnv 获取布尔环境变量
func getBoolEnv(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

// getDurationEnv 获取时间间隔环境变量
func getDurationEnv(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}

// getSliceEnv 获取切片环境变量
func getSliceEnv(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		return strings.Split(value, ",")
	}
	return defaultValue
}

// GetAddress 获取服务器地址
func (c *Config) GetAddress() string {
	return c.Server.Host + ":" + c.Server.Port
}

// IsDevelopment 是否为开发模式
func (c *Config) IsDevelopment() bool {
	return c.Server.Mode == "debug"
}

// IsProduction 是否为生产模式
func (c *Config) IsProduction() bool {
	return c.Server.Mode == "release"
}
