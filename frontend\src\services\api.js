import axios from 'axios';
import toast from 'react-hot-toast';

// 创建 axios 实例
export const authAPI = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8080',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
authAPI.interceptors.request.use(
  (config) => {
    // 添加认证 token
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // 添加请求 ID
    config.headers['X-Request-ID'] = generateRequestId();
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
authAPI.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // 处理常见错误
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          // 未授权，清除本地认证信息
          localStorage.removeItem('authToken');
          localStorage.removeItem('userData');
          toast.error('登录已过期，请重新登录');
          // 可以在这里重定向到登录页面
          window.location.href = '/login';
          break;
          
        case 403:
          toast.error('没有权限访问此资源');
          break;
          
        case 404:
          toast.error('请求的资源不存在');
          break;
          
        case 429:
          toast.error('请求过于频繁，请稍后再试');
          break;
          
        case 500:
          toast.error('服务器内部错误');
          break;
          
        default:
          // 显示后端返回的错误信息
          const message = data?.message || data?.error?.message || '请求失败';
          toast.error(message);
      }
    } else if (error.request) {
      // 网络错误
      toast.error('网络连接失败，请检查网络设置');
    } else {
      // 其他错误
      toast.error('请求配置错误');
    }
    
    return Promise.reject(error);
  }
);

// 用户相关 API
export const userAPI = {
  // 获取用户列表
  getUsers: (params = {}) => {
    return authAPI.get('/api/v1/customers', { params });
  },
  
  // 获取单个用户
  getUser: (id) => {
    return authAPI.get(`/api/v1/customers/${id}`);
  },
  
  // 创建用户（注册）
  createUser: (userData) => {
    return authAPI.post('/api/v1/customers', userData);
  },
  
  // 更新用户
  updateUser: (id, userData) => {
    return authAPI.put(`/api/v1/customers/${id}`, userData);
  },
  
  // 删除用户
  deleteUser: (id) => {
    return authAPI.delete(`/api/v1/customers/${id}`);
  },
  
  // 批量删除用户
  batchDeleteUsers: (ids) => {
    return authAPI.delete('/api/v1/customers/batch', { data: { ids } });
  },
  
  // 更新用户状态
  updateUserStatus: (id, status) => {
    return authAPI.patch(`/api/v1/customers/${id}/status`, { status });
  },
  
  // 搜索用户
  searchUsers: (keyword) => {
    return authAPI.get('/api/v1/customers/search', { 
      params: { keyword } 
    });
  },
  
  // 获取用户统计
  getUserStats: () => {
    return authAPI.get('/api/v1/customers/stats');
  }
};

// 系统相关 API
export const systemAPI = {
  // 健康检查
  healthCheck: () => {
    return authAPI.get('/health');
  },
  
  // 获取系统指标
  getMetrics: () => {
    return authAPI.get('/metrics');
  }
};

// 认证相关 API
export const authService = {
  // 登录
  login: async (credentials) => {
    return authAPI.post('/api/v1/auth/login', credentials);
  },

  // 注册
  register: async (userData) => {
    return authAPI.post('/api/v1/auth/register', userData);
  },

  // 验证 token
  verifyToken: async () => {
    return authAPI.post('/api/v1/auth/validate');
  },

  // 获取用户信息
  getProfile: async () => {
    return authAPI.get('/api/v1/profile');
  },

  // 修改密码
  changePassword: async (passwordData) => {
    return authAPI.post('/api/v1/change-password', passwordData);
  }
};

// 工具函数
function generateRequestId() {
  return 'req_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
}



export default authAPI;
