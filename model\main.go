package model

import (
	"time"
)

// UserStatus 用户状态枚举
type UserStatus string

const (
	UserStatusActive   UserStatus = "active"   // 活跃
	UserStatusInactive UserStatus = "inactive" // 非活跃
	UserStatusBlocked  UserStatus = "blocked"  // 被封禁
	UserStatusDeleted  UserStatus = "deleted"  // 已删除
)

// Customer 用户模型
type Customer struct {
	ID          int        `json:"id" gorm:"primaryKey;autoIncrement"`
	Name        string     `json:"name" binding:"required,min=2,max=50" gorm:"not null;size:50"`
	Gender      string     `json:"gender" binding:"oneof=男 女 其他" gorm:"size:10"`
	Age         int        `json:"age" binding:"min=0,max=150" gorm:"check:age >= 0 AND age <= 150"`
	Phone       string     `json:"phone" binding:"required,len=11" gorm:"uniqueIndex;size:11"`
	Email       string     `json:"email" binding:"required,email" gorm:"uniqueIndex;size:100"`
	Password    string     `json:"-" binding:"required,min=6" gorm:"not null;size:255"` // 密码字段，不在JSON中返回
	Status      UserStatus `json:"status" gorm:"default:active;size:20"`
	Avatar      string     `json:"avatar" gorm:"size:255"`
	Address     string     `json:"address" gorm:"size:255"`
	Description string     `json:"description" gorm:"size:500"`
	CreatedAt   time.Time  `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time  `json:"updated_at" gorm:"autoUpdateTime"`
}

// CreateCustomerRequest 创建用户请求结构
type CreateCustomerRequest struct {
	Name        string `json:"name" binding:"required,min=2,max=50"`
	Gender      string `json:"gender" binding:"oneof=男 女 其他"`
	Age         int    `json:"age" binding:"min=0,max=150"`
	Phone       string `json:"phone" binding:"required,len=11"`
	Email       string `json:"email" binding:"required,email"`
	Password    string `json:"password" binding:"required,min=6"` // 密码字段
	Avatar      string `json:"avatar"`
	Address     string `json:"address"`
	Description string `json:"description"`
}

// UpdateCustomerRequest 更新用户请求结构
type UpdateCustomerRequest struct {
	Name        *string `json:"name" binding:"omitempty,min=2,max=50"`
	Gender      *string `json:"gender" binding:"omitempty,oneof=男 女 其他"`
	Age         *int    `json:"age" binding:"omitempty,min=0,max=150"`
	Phone       *string `json:"phone" binding:"omitempty,len=11"`
	Email       *string `json:"email" binding:"omitempty,email"`
	Avatar      *string `json:"avatar"`
	Address     *string `json:"address"`
	Description *string `json:"description"`
}

// CustomerListRequest 用户列表查询请求
type CustomerListRequest struct {
	Page     int        `form:"page" binding:"omitempty,min=1"`
	PageSize int        `form:"page_size" binding:"omitempty,min=1,max=100"`
	Search   string     `form:"search"`
	Status   UserStatus `form:"status" binding:"omitempty,oneof=active inactive blocked deleted"`
	Gender   string     `form:"gender" binding:"omitempty,oneof=男 女 其他"`
}

// CustomerListResponse 用户列表响应
type CustomerListResponse struct {
	Data       []Customer `json:"data"`
	Total      int64      `json:"total"`
	Page       int        `json:"page"`
	PageSize   int        `json:"page_size"`
	TotalPages int        `json:"total_pages"`
}

// LoginRequest 登录请求结构
type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
}

// RegisterRequest 注册请求结构
type RegisterRequest struct {
	Name        string `json:"name" binding:"required,min=2,max=50"`
	Gender      string `json:"gender" binding:"oneof=男 女 其他"`
	Age         int    `json:"age" binding:"min=0,max=150"`
	Phone       string `json:"phone" binding:"required,len=11"`
	Email       string `json:"email" binding:"required,email"`
	Password    string `json:"password" binding:"required,min=6"`
	Avatar      string `json:"avatar"`
	Address     string `json:"address"`
	Description string `json:"description"`
}

// LoginResponse 登录响应结构
type LoginResponse struct {
	User  *Customer `json:"user"`
	Token string    `json:"token"`
}

// TokenClaims JWT Token 声明结构
type TokenClaims struct {
	UserID int    `json:"user_id"`
	Email  string `json:"email"`
}
