{"ast": null, "code": "var _jsxFileName = \"D:\\\\gohomeworkproject\\\\frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authAPI } from '../services/api';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  // 检查本地存储的认证状态\n  useEffect(() => {\n    const checkAuthStatus = () => {\n      try {\n        const token = localStorage.getItem('authToken');\n        const userData = localStorage.getItem('userData');\n        if (token && userData) {\n          const parsedUser = JSON.parse(userData);\n          setUser(parsedUser);\n          setIsAuthenticated(true);\n          // 设置 API 默认 header\n          authAPI.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n        }\n      } catch (error) {\n        console.error('检查认证状态失败:', error);\n        // 清除无效的本地存储\n        localStorage.removeItem('authToken');\n        localStorage.removeItem('userData');\n      } finally {\n        setLoading(false);\n      }\n    };\n    checkAuthStatus();\n  }, []);\n\n  // 登录函数\n  const login = async credentials => {\n    try {\n      setLoading(true);\n\n      // 调用真实的登录 API\n      const response = await authAPI.post('/api/v1/auth/login', credentials);\n      if (response.data.success) {\n        const {\n          user: userData,\n          token\n        } = response.data.data;\n\n        // 保存到本地存储\n        localStorage.setItem('authToken', token);\n        localStorage.setItem('userData', JSON.stringify(userData));\n\n        // 设置 API 默认 header\n        authAPI.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n\n        // 更新状态\n        setUser(userData);\n        setIsAuthenticated(true);\n        toast.success('登录成功！');\n        return {\n          success: true\n        };\n      } else {\n        toast.error(response.data.message || '登录失败');\n        return {\n          success: false,\n          message: response.data.message\n        };\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('登录错误:', error);\n      const message = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || '登录失败，请稍后重试';\n      toast.error(message);\n      return {\n        success: false,\n        message\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 注册函数\n  const register = async userData => {\n    try {\n      setLoading(true);\n\n      // 调用真实的注册 API\n      const response = await authAPI.post('/api/v1/auth/register', userData);\n      if (response.data.success) {\n        toast.success('注册成功！请登录');\n        return {\n          success: true\n        };\n      } else {\n        toast.error(response.data.message || '注册失败');\n        return {\n          success: false,\n          message: response.data.message\n        };\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('注册错误:', error);\n      const message = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || '注册失败，请稍后重试';\n      toast.error(message);\n      return {\n        success: false,\n        message\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 登出函数\n  const logout = () => {\n    try {\n      // 清除本地存储\n      localStorage.removeItem('authToken');\n      localStorage.removeItem('userData');\n\n      // 清除 API header\n      delete authAPI.defaults.headers.common['Authorization'];\n\n      // 重置状态\n      setUser(null);\n      setIsAuthenticated(false);\n      toast.success('已安全退出');\n    } catch (error) {\n      console.error('登出错误:', error);\n    }\n  };\n\n  // 更新用户信息\n  const updateUser = newUserData => {\n    try {\n      const updatedUser = {\n        ...user,\n        ...newUserData\n      };\n      setUser(updatedUser);\n      localStorage.setItem('userData', JSON.stringify(updatedUser));\n    } catch (error) {\n      console.error('更新用户信息失败:', error);\n    }\n  };\n  const value = {\n    user,\n    loading,\n    isAuthenticated,\n    login,\n    register,\n    logout,\n    updateUser\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 152,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"xBgiRagNfQVCfEr2dT2PptfN+TE=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "authAPI", "toast", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "isAuthenticated", "setIsAuthenticated", "checkAuthStatus", "token", "localStorage", "getItem", "userData", "parsedUser", "JSON", "parse", "defaults", "headers", "common", "error", "console", "removeItem", "login", "credentials", "response", "post", "data", "success", "setItem", "stringify", "message", "_error$response", "_error$response$data", "register", "_error$response2", "_error$response2$data", "logout", "updateUser", "newUserData", "updatedUser", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/gohomeworkproject/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authAPI } from '../services/api';\nimport toast from 'react-hot-toast';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  // 检查本地存储的认证状态\n  useEffect(() => {\n    const checkAuthStatus = () => {\n      try {\n        const token = localStorage.getItem('authToken');\n        const userData = localStorage.getItem('userData');\n        \n        if (token && userData) {\n          const parsedUser = JSON.parse(userData);\n          setUser(parsedUser);\n          setIsAuthenticated(true);\n          // 设置 API 默认 header\n          authAPI.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n        }\n      } catch (error) {\n        console.error('检查认证状态失败:', error);\n        // 清除无效的本地存储\n        localStorage.removeItem('authToken');\n        localStorage.removeItem('userData');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    checkAuthStatus();\n  }, []);\n\n  // 登录函数\n  const login = async (credentials) => {\n    try {\n      setLoading(true);\n\n      // 调用真实的登录 API\n      const response = await authAPI.post('/api/v1/auth/login', credentials);\n\n      if (response.data.success) {\n        const { user: userData, token } = response.data.data;\n\n        // 保存到本地存储\n        localStorage.setItem('authToken', token);\n        localStorage.setItem('userData', JSON.stringify(userData));\n\n        // 设置 API 默认 header\n        authAPI.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n\n        // 更新状态\n        setUser(userData);\n        setIsAuthenticated(true);\n\n        toast.success('登录成功！');\n        return { success: true };\n      } else {\n        toast.error(response.data.message || '登录失败');\n        return { success: false, message: response.data.message };\n      }\n    } catch (error) {\n      console.error('登录错误:', error);\n      const message = error.response?.data?.message || '登录失败，请稍后重试';\n      toast.error(message);\n      return { success: false, message };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 注册函数\n  const register = async (userData) => {\n    try {\n      setLoading(true);\n\n      // 调用真实的注册 API\n      const response = await authAPI.post('/api/v1/auth/register', userData);\n\n      if (response.data.success) {\n        toast.success('注册成功！请登录');\n        return { success: true };\n      } else {\n        toast.error(response.data.message || '注册失败');\n        return { success: false, message: response.data.message };\n      }\n    } catch (error) {\n      console.error('注册错误:', error);\n      const message = error.response?.data?.message || '注册失败，请稍后重试';\n      toast.error(message);\n      return { success: false, message };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 登出函数\n  const logout = () => {\n    try {\n      // 清除本地存储\n      localStorage.removeItem('authToken');\n      localStorage.removeItem('userData');\n      \n      // 清除 API header\n      delete authAPI.defaults.headers.common['Authorization'];\n      \n      // 重置状态\n      setUser(null);\n      setIsAuthenticated(false);\n      \n      toast.success('已安全退出');\n    } catch (error) {\n      console.error('登出错误:', error);\n    }\n  };\n\n  // 更新用户信息\n  const updateUser = (newUserData) => {\n    try {\n      const updatedUser = { ...user, ...newUserData };\n      setUser(updatedUser);\n      localStorage.setItem('userData', JSON.stringify(updatedUser));\n    } catch (error) {\n      console.error('更新用户信息失败:', error);\n    }\n  };\n\n  const value = {\n    user,\n    loading,\n    isAuthenticated,\n    login,\n    register,\n    logout,\n    updateUser\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,WAAW,gBAAGR,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMS,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGV,UAAU,CAACO,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,eAAe,EAAEC,kBAAkB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACAC,SAAS,CAAC,MAAM;IACd,MAAMmB,eAAe,GAAGA,CAAA,KAAM;MAC5B,IAAI;QACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;QAC/C,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;QAEjD,IAAIF,KAAK,IAAIG,QAAQ,EAAE;UACrB,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC;UACvCT,OAAO,CAACU,UAAU,CAAC;UACnBN,kBAAkB,CAAC,IAAI,CAAC;UACxB;UACAjB,OAAO,CAAC0B,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUT,KAAK,EAAE;QACtE;MACF,CAAC,CAAC,OAAOU,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC;QACAT,YAAY,CAACW,UAAU,CAAC,WAAW,CAAC;QACpCX,YAAY,CAACW,UAAU,CAAC,UAAU,CAAC;MACrC,CAAC,SAAS;QACRhB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMc,KAAK,GAAG,MAAOC,WAAW,IAAK;IACnC,IAAI;MACFlB,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMmB,QAAQ,GAAG,MAAMlC,OAAO,CAACmC,IAAI,CAAC,oBAAoB,EAAEF,WAAW,CAAC;MAEtE,IAAIC,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB,MAAM;UAAEzB,IAAI,EAAEU,QAAQ;UAAEH;QAAM,CAAC,GAAGe,QAAQ,CAACE,IAAI,CAACA,IAAI;;QAEpD;QACAhB,YAAY,CAACkB,OAAO,CAAC,WAAW,EAAEnB,KAAK,CAAC;QACxCC,YAAY,CAACkB,OAAO,CAAC,UAAU,EAAEd,IAAI,CAACe,SAAS,CAACjB,QAAQ,CAAC,CAAC;;QAE1D;QACAtB,OAAO,CAAC0B,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUT,KAAK,EAAE;;QAEpE;QACAN,OAAO,CAACS,QAAQ,CAAC;QACjBL,kBAAkB,CAAC,IAAI,CAAC;QAExBhB,KAAK,CAACoC,OAAO,CAAC,OAAO,CAAC;QACtB,OAAO;UAAEA,OAAO,EAAE;QAAK,CAAC;MAC1B,CAAC,MAAM;QACLpC,KAAK,CAAC4B,KAAK,CAACK,QAAQ,CAACE,IAAI,CAACI,OAAO,IAAI,MAAM,CAAC;QAC5C,OAAO;UAAEH,OAAO,EAAE,KAAK;UAAEG,OAAO,EAAEN,QAAQ,CAACE,IAAI,CAACI;QAAQ,CAAC;MAC3D;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MAAA,IAAAY,eAAA,EAAAC,oBAAA;MACdZ,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7B,MAAMW,OAAO,GAAG,EAAAC,eAAA,GAAAZ,KAAK,CAACK,QAAQ,cAAAO,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBL,IAAI,cAAAM,oBAAA,uBAApBA,oBAAA,CAAsBF,OAAO,KAAI,YAAY;MAC7DvC,KAAK,CAAC4B,KAAK,CAACW,OAAO,CAAC;MACpB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEG;MAAQ,CAAC;IACpC,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4B,QAAQ,GAAG,MAAOrB,QAAQ,IAAK;IACnC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMmB,QAAQ,GAAG,MAAMlC,OAAO,CAACmC,IAAI,CAAC,uBAAuB,EAAEb,QAAQ,CAAC;MAEtE,IAAIY,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBpC,KAAK,CAACoC,OAAO,CAAC,UAAU,CAAC;QACzB,OAAO;UAAEA,OAAO,EAAE;QAAK,CAAC;MAC1B,CAAC,MAAM;QACLpC,KAAK,CAAC4B,KAAK,CAACK,QAAQ,CAACE,IAAI,CAACI,OAAO,IAAI,MAAM,CAAC;QAC5C,OAAO;UAAEH,OAAO,EAAE,KAAK;UAAEG,OAAO,EAAEN,QAAQ,CAACE,IAAI,CAACI;QAAQ,CAAC;MAC3D;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MAAA,IAAAe,gBAAA,EAAAC,qBAAA;MACdf,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7B,MAAMW,OAAO,GAAG,EAAAI,gBAAA,GAAAf,KAAK,CAACK,QAAQ,cAAAU,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBR,IAAI,cAAAS,qBAAA,uBAApBA,qBAAA,CAAsBL,OAAO,KAAI,YAAY;MAC7DvC,KAAK,CAAC4B,KAAK,CAACW,OAAO,CAAC;MACpB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEG;MAAQ,CAAC;IACpC,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+B,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAI;MACF;MACA1B,YAAY,CAACW,UAAU,CAAC,WAAW,CAAC;MACpCX,YAAY,CAACW,UAAU,CAAC,UAAU,CAAC;;MAEnC;MACA,OAAO/B,OAAO,CAAC0B,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;;MAEvD;MACAf,OAAO,CAAC,IAAI,CAAC;MACbI,kBAAkB,CAAC,KAAK,CAAC;MAEzBhB,KAAK,CAACoC,OAAO,CAAC,OAAO,CAAC;IACxB,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAMkB,UAAU,GAAIC,WAAW,IAAK;IAClC,IAAI;MACF,MAAMC,WAAW,GAAG;QAAE,GAAGrC,IAAI;QAAE,GAAGoC;MAAY,CAAC;MAC/CnC,OAAO,CAACoC,WAAW,CAAC;MACpB7B,YAAY,CAACkB,OAAO,CAAC,UAAU,EAAEd,IAAI,CAACe,SAAS,CAACU,WAAW,CAAC,CAAC;IAC/D,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;EAED,MAAMqB,KAAK,GAAG;IACZtC,IAAI;IACJE,OAAO;IACPE,eAAe;IACfgB,KAAK;IACLW,QAAQ;IACRG,MAAM;IACNC;EACF,CAAC;EAED,oBACE5C,OAAA,CAACC,WAAW,CAAC+C,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAxC,QAAA,EAChCA;EAAQ;IAAA0C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC5C,GAAA,CA7IWF,YAAY;AAAA+C,EAAA,GAAZ/C,YAAY;AAAA,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}