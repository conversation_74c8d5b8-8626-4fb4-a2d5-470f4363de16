package handler

import (
	"net/http"
	"strings"

	"goHomework/model"
	"goHomework/service"

	"github.com/gin-gonic/gin"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	authService *service.AuthService
}

// NewAuthHandler 创建认证处理器实例
func NewAuthHandler(authService *service.AuthService) *AuthHandler {
	return &AuthHandler{
		authService: authService,
	}
}

// Login 用户登录
func (h *AuthHandler) Login(c *gin.Context) {
	var req model.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, model.APIResponse{
			Success:   false,
			Message:   "请求参数错误",
			Error:     &model.ErrorInfo{Code: "INVALID_REQUEST", Message: err.Error()},
			Timestamp: model.GetCurrentTime(),
			RequestID: c.<PERSON>ead<PERSON>("X-Request-ID"),
		})
		return
	}

	loginResponse, err := h.authService.Login(req)
	if err != nil {
		c.JSON(http.StatusUnauthorized, model.APIResponse{
			Success:   false,
			Message:   err.Error(),
			Error:     &model.ErrorInfo{Code: "LOGIN_FAILED", Message: err.Error()},
			Timestamp: model.GetCurrentTime(),
			RequestID: c.GetHeader("X-Request-ID"),
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Success:   true,
		Message:   "登录成功",
		Data:      loginResponse,
		Timestamp: model.GetCurrentTime(),
		RequestID: c.GetHeader("X-Request-ID"),
	})
}

// Register 用户注册
func (h *AuthHandler) Register(c *gin.Context) {
	var req model.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, model.APIResponse{
			Success:   false,
			Message:   "请求参数错误",
			Error:     &model.ErrorInfo{Code: "INVALID_REQUEST", Message: err.Error()},
			Timestamp: model.GetCurrentTime(),
			RequestID: c.GetHeader("X-Request-ID"),
		})
		return
	}

	customer, err := h.authService.Register(req)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.APIResponse{
			Success:   false,
			Message:   err.Error(),
			Error:     &model.ErrorInfo{Code: "REGISTER_FAILED", Message: err.Error()},
			Timestamp: model.GetCurrentTime(),
			RequestID: c.GetHeader("X-Request-ID"),
		})
		return
	}

	c.JSON(http.StatusCreated, model.APIResponse{
		Success:   true,
		Message:   "注册成功",
		Data:      customer,
		Timestamp: model.GetCurrentTime(),
		RequestID: c.GetHeader("X-Request-ID"),
	})
}

// GetProfile 获取用户信息
func (h *AuthHandler) GetProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, model.APIResponse{
			Success:   false,
			Message:   "未授权访问",
			Error:     &model.ErrorInfo{Code: "UNAUTHORIZED", Message: "用户未登录"},
			Timestamp: model.GetCurrentTime(),
			RequestID: c.GetHeader("X-Request-ID"),
		})
		return
	}

	customer, err := h.authService.GetUserByID(userID.(int))
	if err != nil {
		c.JSON(http.StatusNotFound, model.APIResponse{
			Success:   false,
			Message:   err.Error(),
			Error:     &model.ErrorInfo{Code: "USER_NOT_FOUND", Message: err.Error()},
			Timestamp: model.GetCurrentTime(),
			RequestID: c.GetHeader("X-Request-ID"),
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Success:   true,
		Message:   "获取用户信息成功",
		Data:      customer,
		Timestamp: model.GetCurrentTime(),
		RequestID: c.GetHeader("X-Request-ID"),
	})
}

// ChangePassword 修改密码
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, model.APIResponse{
			Success:   false,
			Message:   "未授权访问",
			Error:     &model.ErrorInfo{Code: "UNAUTHORIZED", Message: "用户未登录"},
			Timestamp: model.GetCurrentTime(),
			RequestID: c.GetHeader("X-Request-ID"),
		})
		return
	}

	var req struct {
		OldPassword string `json:"old_password" binding:"required,min=6"`
		NewPassword string `json:"new_password" binding:"required,min=6"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, model.APIResponse{
			Success:   false,
			Message:   "请求参数错误",
			Error:     &model.ErrorInfo{Code: "INVALID_REQUEST", Message: err.Error()},
			Timestamp: model.GetCurrentTime(),
			RequestID: c.GetHeader("X-Request-ID"),
		})
		return
	}

	err := h.authService.ChangePassword(userID.(int), req.OldPassword, req.NewPassword)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.APIResponse{
			Success:   false,
			Message:   err.Error(),
			Error:     &model.ErrorInfo{Code: "CHANGE_PASSWORD_FAILED", Message: err.Error()},
			Timestamp: model.GetCurrentTime(),
			RequestID: c.GetHeader("X-Request-ID"),
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Success:   true,
		Message:   "密码修改成功",
		Timestamp: model.GetCurrentTime(),
		RequestID: c.GetHeader("X-Request-ID"),
	})
}

// ValidateToken 验证token
func (h *AuthHandler) ValidateToken(c *gin.Context) {
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.JSON(http.StatusUnauthorized, model.APIResponse{
			Success:   false,
			Message:   "缺少Authorization头",
			Error:     &model.ErrorInfo{Code: "MISSING_TOKEN", Message: "Authorization header is required"},
			Timestamp: model.GetCurrentTime(),
			RequestID: c.GetHeader("X-Request-ID"),
		})
		return
	}

	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	if tokenString == authHeader {
		c.JSON(http.StatusUnauthorized, model.APIResponse{
			Success:   false,
			Message:   "Token格式错误",
			Error:     &model.ErrorInfo{Code: "INVALID_TOKEN_FORMAT", Message: "Token must be in Bearer format"},
			Timestamp: model.GetCurrentTime(),
			RequestID: c.GetHeader("X-Request-ID"),
		})
		return
	}

	claims, err := h.authService.ValidateToken(tokenString)
	if err != nil {
		c.JSON(http.StatusUnauthorized, model.APIResponse{
			Success:   false,
			Message:   "Token验证失败",
			Error:     &model.ErrorInfo{Code: "TOKEN_VALIDATION_FAILED", Message: err.Error()},
			Timestamp: model.GetCurrentTime(),
			RequestID: c.GetHeader("X-Request-ID"),
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Success:   true,
		Message:   "Token验证成功",
		Data:      claims,
		Timestamp: model.GetCurrentTime(),
		RequestID: c.GetHeader("X-Request-ID"),
	})
}

// AuthMiddleware JWT认证中间件
func (h *AuthHandler) AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, model.APIResponse{
				Success:   false,
				Message:   "缺少Authorization头",
				Error:     &model.ErrorInfo{Code: "MISSING_TOKEN", Message: "Authorization header is required"},
				Timestamp: model.GetCurrentTime(),
				RequestID: c.GetHeader("X-Request-ID"),
			})
			c.Abort()
			return
		}

		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			c.JSON(http.StatusUnauthorized, model.APIResponse{
				Success:   false,
				Message:   "Token格式错误",
				Error:     &model.ErrorInfo{Code: "INVALID_TOKEN_FORMAT", Message: "Token must be in Bearer format"},
				Timestamp: model.GetCurrentTime(),
				RequestID: c.GetHeader("X-Request-ID"),
			})
			c.Abort()
			return
		}

		claims, err := h.authService.ValidateToken(tokenString)
		if err != nil {
			c.JSON(http.StatusUnauthorized, model.APIResponse{
				Success:   false,
				Message:   "Token验证失败",
				Error:     &model.ErrorInfo{Code: "TOKEN_VALIDATION_FAILED", Message: err.Error()},
				Timestamp: model.GetCurrentTime(),
				RequestID: c.GetHeader("X-Request-ID"),
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("user_email", claims.Email)
		c.Next()
	}
}
